/**
 * نظام مراقبة الحضور المتقدم مع منع التلاعب
 */

class RealTimeAttendanceMonitor {
    constructor(lessonId, lessonType, displayName) {
        this.lessonId = lessonId;
        this.lessonType = lessonType;
        this.displayName = displayName;
        this.monitoringId = null;
        this.heartbeatInterval = null;
        this.timerInterval = null;
        this.isActive = false;
        this.heartbeatCount = 0;
        this.warningLevel = 'normal';
        this.startTime = null;
        this.heartbeatIntervalSeconds = 15; // محسن من 20 إلى 15

        // تكامل Jitsi
        this.jitsiIntegration = null;
        this.roomName = `lesson_${lessonType}_${lessonId}`;

        // إعدادات المدير
        this.isAdminMode = false;
        this.jitsiContainerId = 'jitsi-container';
        this.startButtonId = 'start-jitsi-lesson';

        this.init();
    }
    
    async init() {
        try {
            console.log('🚀 بدء تهيئة نظام المراقبة...');
            
            // بدء المراقبة
            const response = await fetch('/lessons/api/monitoring/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    action: 'start_monitoring',
                    lesson_type: this.lessonType,
                    lesson_id: this.lessonId,
                    user_role: 'student'
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.monitoringId = data.monitoring_id;
                this.externalUrl = data.external_url;
                this.heartbeatIntervalSeconds = data.heartbeat_interval || 20;
                
                this.setupUI();
                this.setupEventListeners();
                this.updateStatus('intended', 'تم تهيئة النظام - جاهز للبدء');
                
                console.log('✅ تم بدء المراقبة بنجاح');
                this.showNotification('تم تهيئة نظام المراقبة بنجاح', 'success');
            } else {
                console.error('❌ فشل في بدء المراقبة:', data.error);
                this.showError('فشل في بدء نظام المراقبة: ' + data.error);
            }
        } catch (error) {
            console.error('❌ خطأ في تهيئة المراقبة:', error);
            this.showError('خطأ في الاتصال بالخادم');
        }
    }
    
    setupUI() {
        // تحديث أزرار التحكم
        const startButton = document.getElementById(this.startButtonId);
        const confirmButton = document.getElementById('mark-attendance');
        const leaveButton = document.getElementById('mark-leave');

        if (startButton) {
            startButton.onclick = () => this.startJitsiLesson();
        }

        if (confirmButton && !this.isAdminMode) {
            confirmButton.onclick = () => this.confirmAttendance();
        }

        if (leaveButton && !this.isAdminMode) {
            leaveButton.onclick = () => this.markLeave();
        }

        // للمدير: بدء Jitsi تلقائياً
        if (this.isAdminMode) {
            // بدء Jitsi تلقائياً للمدير
            setTimeout(() => {
                this.startJitsiLesson();
            }, 2000);
        } else {
            // للطلاب والمعلمين
            if (startButton) {
                // إظهار زر البدء
                startButton.style.display = 'inline-block';
            }

            // إظهار حاوي Jitsi
            const jitsiContainer = document.getElementById(this.jitsiContainerId);
            if (jitsiContainer) {
                jitsiContainer.style.display = 'block';
            }
        }
    }
    
    setupEventListeners() {
        // مراقبة إغلاق الصفحة
        window.addEventListener('beforeunload', (e) => {
            if (this.isActive) {
                e.preventDefault();
                e.returnValue = 'هل أنت متأكد من مغادرة الحصة؟ سيتم تسجيل خروجك تلقائياً.';
                this.markLeave(false);
            }
        });
        
        // مراقبة تغيير visibility
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });
        
        // مراقبة فقدان التركيز
        window.addEventListener('blur', () => {
            this.handleWindowBlur();
        });
        
        window.addEventListener('focus', () => {
            this.handleWindowFocus();
        });
        
        // مراقبة تغيير حجم النافذة
        window.addEventListener('resize', () => {
            this.handleWindowResize();
        });
    }
    
    async startJitsiLesson() {
        try {
            console.log('🚀 بدء تشغيل Jitsi...');

            // إنشاء تكامل Jitsi
            this.jitsiIntegration = new JitsiIntegration(
                this.roomName,
                this.displayName,
                this.monitoringId,
                this.isAdminMode
            );

            // تحديث حالة الواجهة
            const statusMessage = this.isAdminMode ?
                'تم تشغيل Jitsi - وضع المراقبة نشط' :
                'تم تشغيل Jitsi - يرجى الانضمام للحصة';
            this.updateStatus('intended', statusMessage);

            // تفعيل زر تأكيد الحضور بعد 5 ثوانِ (ليس للمدير)
            if (!this.isAdminMode) {
                setTimeout(() => {
                    const confirmButton = document.getElementById('mark-attendance');
                    if (confirmButton) {
                        confirmButton.disabled = false;
                        confirmButton.classList.remove('opacity-50');
                    }
                }, 5000);
            }

            const successMessage = this.isAdminMode ?
                'تم تشغيل Jitsi بنجاح - وضع المراقبة' :
                'تم تشغيل Jitsi بنجاح - يرجى الانضمام للحصة';
            this.showNotification(successMessage, 'success');

        } catch (error) {
            console.error('❌ خطأ في تشغيل Jitsi:', error);
            this.showError('خطأ في تشغيل Jitsi');
        }
    }
    
    monitorExternalWindow() {
        if (!this.externalWindow) return;
        
        const checkWindow = setInterval(() => {
            if (this.externalWindow.closed) {
                clearInterval(checkWindow);
                if (this.isActive) {
                    this.showNotification('تم إغلاق نافذة الحصة', 'warning');
                    // يمكن إضافة منطق إضافي هنا
                }
            }
        }, 1000);
    }
    
    async confirmAttendance() {
        if (!this.monitoringId) {
            this.showError('معرف المراقبة غير متوفر');
            return;
        }
        
        try {
            const response = await fetch('/lessons/api/monitoring/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    action: 'confirm_attendance',
                    monitoring_id: this.monitoringId
                })
            });
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.isActive = true;
                this.startTime = new Date();
                this.startHeartbeat();
                this.startTimer();
                this.updateStatus('confirmed', 'تم تأكيد الحضور - المراقبة نشطة');
                
                // تفعيل زر الخروج
                const leaveButton = document.getElementById('mark-leave');
                if (leaveButton) {
                    leaveButton.disabled = false;
                    leaveButton.classList.remove('opacity-50');
                }
                
                // تعطيل زر تأكيد الحضور
                const confirmButton = document.getElementById('mark-attendance');
                if (confirmButton) {
                    confirmButton.disabled = true;
                    confirmButton.classList.add('opacity-50');
                    confirmButton.innerHTML = '<i class="fas fa-check ml-2"></i>تم التأكيد';
                }
                
                this.showNotification('تم تأكيد حضورك بنجاح - بدأت المراقبة', 'success');
            } else {
                this.showError('فشل في تأكيد الحضور: ' + data.message);
            }
        } catch (error) {
            console.error('❌ خطأ في تأكيد الحضور:', error);
            this.showError('خطأ في الاتصال بالخادم');
        }
    }
    
    startHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }
        
        this.heartbeatInterval = setInterval(() => {
            this.sendHeartbeat();
        }, this.heartbeatIntervalSeconds * 1000);
        
        console.log(`🔄 بدء إرسال heartbeat كل ${this.heartbeatIntervalSeconds} ثانية`);
    }
    
    startTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
        }
        
        this.timerInterval = setInterval(() => {
            this.updateTimer();
        }, 1000);
    }
    
    updateTimer() {
        if (!this.startTime) return;
        
        const now = new Date();
        const elapsed = Math.floor((now - this.startTime) / 1000);
        
        const hours = Math.floor(elapsed / 3600);
        const minutes = Math.floor((elapsed % 3600) / 60);
        const seconds = elapsed % 60;
        
        const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        const timerDisplay = document.getElementById('timer-display');
        if (timerDisplay) {
            timerDisplay.textContent = timeString;
        }
    }
    
    async sendHeartbeat() {
        if (!this.isActive || !this.monitoringId) return;

        try {
            // الحصول على بيانات Jitsi
            const jitsiData = this.jitsiIntegration ? this.jitsiIntegration.getJitsiData() : {
                isConnected: false,
                participantId: null,
                cameraEnabled: false,
                micEnabled: false,
                connectionQuality: 'unknown',
                participantCount: 0
            };

            const heartbeatData = {
                action: 'heartbeat',
                monitoring_id: this.monitoringId,
                page_visible: !document.hidden,
                timestamp: new Date().toISOString(),
                browser_info: this.getBrowserInfo(),
                screen_resolution: `${screen.width}x${screen.height}`,
                browser_language: navigator.language,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                connection_quality: navigator.onLine ? 'good' : 'poor',
                activity_level: 'normal',
                // بيانات Jitsi الجديدة
                jitsi_participant_count: jitsiData.participantCount,
                jitsi_connection_quality: jitsiData.connectionQuality,
                iframe_focused: document.activeElement && document.activeElement.tagName === 'IFRAME',
                camera_enabled: jitsiData.cameraEnabled,
                mic_enabled: jitsiData.micEnabled
            };
            
            const response = await fetch('/lessons/api/monitoring/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify(heartbeatData)
            });
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.heartbeatCount++;
                this.updateMonitoringUI(data);
                
                // فحص التحذيرات
                if (data.warning_level !== this.warningLevel) {
                    this.handleWarningLevelChange(data.warning_level, data.warning_message);
                }
                
                // فحص مستوى خطر التلاعب
                if (data.fraud_risk_level === 'high') {
                    this.handleFraudDetection();
                }
            } else {
                console.error('❌ فشل heartbeat:', data.message);
                this.handleMissedHeartbeat();
            }
        } catch (error) {
            console.error('❌ خطأ في إرسال heartbeat:', error);
            this.handleMissedHeartbeat();
        }
    }
    
    updateMonitoringUI(data) {
        // تحديث عداد النبضات
        const heartbeatElement = document.getElementById('heartbeat-count');
        if (heartbeatElement) {
            heartbeatElement.textContent = data.heartbeat_count || this.heartbeatCount;
        }
        
        // تحديث نقاط الجودة
        const qualityElement = document.getElementById('quality-score');
        if (qualityElement) {
            const score = Math.round(data.quality_score || 100);
            qualityElement.textContent = score;
            
            // تغيير اللون حسب النقاط
            qualityElement.className = 'font-bold text-lg';
            if (score >= 80) {
                qualityElement.classList.add('text-green-600');
            } else if (score >= 60) {
                qualityElement.classList.add('text-yellow-600');
            } else {
                qualityElement.classList.add('text-red-600');
            }
        }
        
        // تحديث جودة الاتصال
        const connectionElement = document.getElementById('connection-quality');
        if (connectionElement) {
            const reliability = data.attendance_reliability || 100;
            connectionElement.className = 'font-bold text-lg';
            
            if (reliability >= 90) {
                connectionElement.textContent = 'ممتاز';
                connectionElement.classList.add('text-green-600');
            } else if (reliability >= 70) {
                connectionElement.textContent = 'جيد';
                connectionElement.classList.add('text-yellow-600');
            } else {
                connectionElement.textContent = 'ضعيف';
                connectionElement.classList.add('text-red-600');
            }
        }
    }
    
    handleWarningLevelChange(newLevel, message) {
        this.warningLevel = newLevel;
        
        switch(newLevel) {
            case 'info':
                this.showNotification(message, 'warning');
                break;
            case 'warning':
                this.showWarningPopup(message);
                break;
            case 'critical':
                this.showCriticalWarning(message);
                break;
            case 'disconnect':
                this.handleForceDisconnect(message);
                break;
        }
    }
    
    showWarningPopup(message) {
        if (confirm(`⚠️ تحذير هام:\n${message}\n\nهل تريد المتابعة؟`)) {
            this.showNotification('يرجى التأكد من استقرار الاتصال', 'warning');
        }
    }
    
    showCriticalWarning(message) {
        alert(`🚨 تحذير نهائي:\n${message}`);
        this.updateStatus('critical', 'حالة حرجة - قد يتم قطع الاتصال');
    }
    
    handleForceDisconnect(message) {
        alert(`❌ تم قطع الاتصال:\n${message}`);
        this.isActive = false;
        this.stopMonitoring();
        this.updateStatus('disconnected', 'تم قطع الاتصال بسبب عدم الاستجابة');
    }
    
    handleFraudDetection() {
        this.showNotification('تم اكتشاف نشاط مشبوه - يرجى التأكد من الحضور الفعلي', 'error');
        this.updateStatus('suspicious', 'تم اكتشاف نشاط مشبوه');
    }
    
    handleMissedHeartbeat() {
        console.warn('⚠️ فشل في إرسال heartbeat');
        this.showNotification('مشكلة في الاتصال - يرجى التحقق من الإنترنت', 'warning');
    }
    
    getBrowserInfo() {
        return {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            screen: {
                width: screen.width,
                height: screen.height,
                colorDepth: screen.colorDepth
            },
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            }
        };
    }
    
    handleVisibilityChange() {
        if (document.hidden) {
            console.log('⚠️ تم إخفاء الصفحة');
            this.showNotification('تم إخفاء الصفحة - قد يؤثر على جودة الحضور', 'warning');
        } else {
            console.log('✅ تم إظهار الصفحة');
        }
    }
    
    handleWindowBlur() {
        console.log('⚠️ فقدان تركيز النافذة');
    }
    
    handleWindowFocus() {
        console.log('✅ استعادة تركيز النافذة');
    }
    
    handleWindowResize() {
        console.log('📐 تم تغيير حجم النافذة');
    }
    
    async markLeave(showConfirm = true) {
        if (showConfirm && !confirm('هل أنت متأكد من تسجيل الخروج من الحصة؟')) {
            return;
        }
        
        if (!this.monitoringId) return;
        
        try {
            const response = await fetch('/lessons/api/monitoring/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    action: 'mark_leave',
                    monitoring_id: this.monitoringId
                })
            });
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.isActive = false;
                this.stopMonitoring();

                // إنهاء جلسة Jitsi
                if (this.jitsiIntegration) {
                    this.jitsiIntegration.hangUp();
                    this.jitsiIntegration.dispose();
                    this.jitsiIntegration = null;
                }

                this.updateStatus('completed', 'تم تسجيل الخروج بنجاح');
                this.showNotification('تم تسجيل خروجك من الحصة', 'success');

                // إظهار نافذة التقييم
                setTimeout(() => {
                    this.showRatingModal();
                }, 1000);
            } else {
                this.showError('فشل في تسجيل الخروج: ' + data.message);
            }
        } catch (error) {
            console.error('❌ خطأ في تسجيل الخروج:', error);
            this.showError('خطأ في الاتصال بالخادم');
        }
    }
    
    stopMonitoring() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
        
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
    }
    
    updateStatus(status, message) {
        const statusIndicator = document.getElementById('status-indicator');
        const statusText = document.getElementById('status-text');
        
        if (statusIndicator && statusText) {
            statusText.textContent = message;
            
            // تحديث لون المؤشر
            statusIndicator.className = 'w-3 h-3 rounded-full ml-3 status-indicator';
            switch(status) {
                case 'intended':
                    statusIndicator.classList.add('bg-yellow-400');
                    break;
                case 'confirmed':
                case 'active':
                    statusIndicator.classList.add('bg-green-400');
                    break;
                case 'warning':
                    statusIndicator.classList.add('bg-orange-400');
                    break;
                case 'critical':
                case 'suspicious':
                    statusIndicator.classList.add('bg-red-400');
                    break;
                case 'completed':
                    statusIndicator.classList.add('bg-blue-400');
                    break;
                default:
                    statusIndicator.classList.add('bg-gray-400');
            }
        }
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `<i class="fas fa-info-circle ml-2"></i>${message}`;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-in forwards';
            setTimeout(() => notification.remove(), 300);
        }, 5000);
    }
    
    showError(message) {
        this.showNotification(message, 'error');
        console.error('❌', message);
    }
    
    showRatingModal() {
        const modal = document.getElementById('rating-modal');
        if (modal) {
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
            this.setupRatingForm();
        }
    }

    setupRatingForm() {
        // إعداد نظام النجوم
        const starRatings = document.querySelectorAll('.star-rating');
        starRatings.forEach(rating => {
            const stars = rating.querySelectorAll('.star');
            const ratingName = rating.dataset.rating;

            stars.forEach((star, index) => {
                star.addEventListener('click', () => {
                    const value = index + 1;

                    // تحديث النجوم المرئية
                    stars.forEach((s, i) => {
                        if (i < value) {
                            s.classList.add('active');
                        } else {
                            s.classList.remove('active');
                        }
                    });

                    // تحديث القيمة المخفية
                    const hiddenInput = document.getElementById(ratingName);
                    if (hiddenInput) {
                        hiddenInput.value = value;
                    }
                });

                // تأثير hover
                star.addEventListener('mouseenter', () => {
                    stars.forEach((s, i) => {
                        if (i <= index) {
                            s.style.color = '#fbbf24';
                        } else {
                            s.style.color = '#d1d5db';
                        }
                    });
                });
            });

            // إعادة تعيين الألوان عند مغادرة المنطقة
            rating.addEventListener('mouseleave', () => {
                stars.forEach((star, i) => {
                    if (star.classList.contains('active')) {
                        star.style.color = '#f59e0b';
                    } else {
                        star.style.color = '#d1d5db';
                    }
                });
            });
        });

        // إعداد نموذج الإرسال
        const ratingForm = document.getElementById('rating-form');
        if (ratingForm) {
            ratingForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitRating();
            });
        }
    }

    async submitRating() {
        try {
            // جمع بيانات التقييم
            const formData = new FormData(document.getElementById('rating-form'));
            const ratingData = {
                lesson_id: this.lessonId,
                lesson_type: this.lessonType,
                overall_rating: formData.get('overall_rating'),
                lesson_quality: formData.get('lesson_quality'),
                teacher_interaction: formData.get('teacher_interaction'),
                technical_quality: formData.get('technical_quality'),
                punctuality: formData.get('punctuality') || null,
                lesson_preparation: formData.get('lesson_preparation') || null,
                comment: formData.get('comment') || ''
            };

            // التحقق من التقييمات المطلوبة
            const requiredFields = ['overall_rating', 'lesson_quality', 'teacher_interaction', 'technical_quality'];
            for (const field of requiredFields) {
                if (!ratingData[field] || ratingData[field] < 1 || ratingData[field] > 5) {
                    this.showError(`يرجى تقييم ${this.getFieldDisplayName(field)}`);
                    return;
                }
            }

            // إرسال التقييم
            const response = await fetch('/lessons/api/submit-unified-rating/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify(ratingData)
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('تم إرسال تقييمك بنجاح! شكراً لك', 'success');

                // إغلاق النافذة والانتقال للوحة التحكم
                setTimeout(() => {
                    this.closeRatingModal();
                    window.location.href = '/dashboard/student/';
                }, 2000);
            } else {
                this.showError('فشل في إرسال التقييم: ' + data.error);
            }

        } catch (error) {
            console.error('❌ خطأ في إرسال التقييم:', error);
            this.showError('خطأ في الاتصال بالخادم');
        }
    }

    getFieldDisplayName(field) {
        const fieldNames = {
            'overall_rating': 'التقييم العام',
            'lesson_quality': 'جودة المحتوى',
            'teacher_interaction': 'تفاعل المعلم',
            'technical_quality': 'الجودة التقنية'
        };
        return fieldNames[field] || field;
    }

    closeRatingModal() {
        const modal = document.getElementById('rating-modal');
        if (modal) {
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }
    }
    
    getCSRFToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }
}

// دالة تخطي التقييم
function skipRating() {
    if (confirm('هل أنت متأكد من تخطي تقييم الحصة؟ تقييمك يساعدنا على تحسين الخدمة.')) {
        const modal = document.getElementById('rating-modal');
        if (modal) {
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // الانتقال للوحة التحكم
        window.location.href = '/dashboard/student/';
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // الحصول على معرف الحصة من الصفحة
    const lessonId = window.lessonId || document.querySelector('[data-lesson-id]')?.dataset.lessonId;
    
    if (lessonId) {
        const monitor = new RealTimeAttendanceMonitor(lessonId, 'live');
        window.attendanceMonitor = monitor;
    } else {
        console.error('❌ لم يتم العثور على معرف الحصة');
    }
});
