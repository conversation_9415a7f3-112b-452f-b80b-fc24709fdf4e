{% extends 'base.html' %}
{% load static %}

{% block title %}{{ live_lesson.title }} - حصة مباشرة (معلم){% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/jitsi-monitoring.css' %}">
<style>
    .lesson-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    }

    .btn-primary:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }

    .btn-secondary {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
    }

    .btn-secondary:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }

    .btn-danger {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
    }

    .btn-danger:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }
    
    .status-indicator {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }
    
    .monitoring-panel {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border: 2px solid #0ea5e9;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    
    .teacher-panel {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        border: 2px solid #f59e0b;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    
    .student-panel {
        background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
        border: 2px solid #10b981;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    
    .quality-card {
        background: white;
        border-radius: 8px;
        padding: 16px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    
    .quality-card:hover {
        transform: translateY(-2px);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s;
        cursor: pointer;
    }
    
    .btn-primary:hover:not(:disabled) {
        background: linear-gradient(135deg, #047857 0%, #059669 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    .btn-secondary {
        background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s;
        cursor: pointer;
    }
    
    .btn-danger {
        background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s;
        cursor: pointer;
    }
    
    .btn-warning {
        background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s;
        cursor: pointer;
    }
</style>
{% endblock %}

{% block content %}
<div class="lesson-container">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
        <div class="flex items-center justify-between mb-4">
            <h1 class="text-2xl font-bold text-gray-800">
                <i class="fas fa-chalkboard-teacher text-blue-600 ml-2"></i>
                {{ live_lesson.title }} (معلم)
            </h1>
            <div class="flex items-center space-x-4">
                <span class="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    {{ live_lesson.get_status_display }}
                </span>
                {% if live_lesson.status == 'scheduled' %}
                <button id="start-lesson-btn" class="btn-primary">
                    <i class="fas fa-play ml-2"></i>
                    بدء الحصة
                </button>
                {% elif live_lesson.status == 'live' %}
                <button id="end-lesson-btn" class="btn-danger">
                    <i class="fas fa-stop ml-2"></i>
                    إنهاء الحصة
                </button>
                {% endif %}
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="text-center">
                <i class="fas fa-user-graduate text-green-500 text-2xl mb-2"></i>
                <p class="text-sm text-gray-600">الطالب</p>
                <p class="font-semibold">{{ live_lesson.student.get_full_name }}</p>
            </div>
            <div class="text-center">
                <i class="fas fa-clock text-orange-500 text-2xl mb-2"></i>
                <p class="text-sm text-gray-600">مدة الحصة</p>
                <p class="font-semibold">{{ live_lesson.duration_minutes }} دقيقة</p>
            </div>
            <div class="text-center">
                <i class="fas fa-calendar text-purple-500 text-2xl mb-2"></i>
                <p class="text-sm text-gray-600">موعد الحصة</p>
                <p class="font-semibold">{{ live_lesson.scheduled_date|date:"H:i" }}</p>
            </div>
            <div class="text-center">
                <i class="fas fa-stopwatch text-red-500 text-2xl mb-2"></i>
                <p class="text-sm text-gray-600">الوقت المنقضي</p>
                <p id="lesson-timer" class="font-semibold">00:00:00</p>
            </div>
        </div>
    </div>

    {% if live_lesson.status == 'scheduled' %}
    <!-- حالة الانتظار -->
    <div class="bg-white rounded-lg shadow-lg p-8 text-center">
        <i class="fas fa-play-circle text-6xl text-blue-400 mb-4"></i>
        <h3 class="text-xl font-bold text-gray-800 mb-2">جاهز لبدء الحصة</h3>
        <p class="text-gray-600 mb-4">اضغط على "بدء الحصة" لبدء الحصة المباشرة</p>
        <p class="text-sm text-gray-500">
            <i class="fas fa-info-circle ml-1"></i>
            سيتم إشعار الطالب تلقائياً عند بدء الحصة
        </p>
    </div>
    
    {% else %}
    <!-- نظام المراقبة للمعلم -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- لوحة المعلم -->
        <div class="teacher-panel p-6">
            <h4 class="text-lg font-bold text-gray-800 mb-4">
                <i class="fas fa-user-tie text-blue-600 ml-2"></i>
                مراقبة المعلم
            </h4>
            
            <!-- حالة المعلم -->
            <div id="teacher-status" class="mb-4">
                <div class="flex items-center justify-between bg-white rounded-lg p-4 shadow-sm">
                    <div class="flex items-center">
                        <div id="teacher-status-indicator" class="w-3 h-3 rounded-full bg-gray-400 ml-3 status-indicator"></div>
                        <span id="teacher-status-text" class="font-medium text-gray-700">جاري التهيئة...</span>
                    </div>
                    <div id="teacher-timer" class="text-sm text-gray-600">
                        <i class="fas fa-stopwatch ml-1"></i>
                        <span id="teacher-timer-display">00:00:00</span>
                    </div>
                </div>
            </div>

            <!-- حاوي Jitsi للمعلم - تصميم كامل -->
            <div class="mb-6">
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="flex items-center justify-between mb-4">
                        <h5 class="font-bold text-gray-800">
                            <i class="fas fa-video text-blue-600 ml-2"></i>
                            غرفة الحصة المباشرة
                        </h5>
                        <div class="flex items-center gap-3">
                            <span id="teacher-status-text" class="text-sm text-gray-600">جاهز للبدء</span>
                            <span id="teacher-status-indicator" class="w-3 h-3 rounded-full bg-yellow-500 animate-pulse"></span>
                            <button id="teacher-start-jitsi" class="btn-primary">
                                <i class="fas fa-play ml-2"></i>
                                بدء الحصة
                            </button>
                        </div>
                    </div>

                    <!-- إطار Jitsi للمعلم - حجم كامل -->
                    <div id="teacher-jitsi-container" style="width: 100%; height: 500px; border-radius: 8px; overflow: hidden; background: #000; display: block; border: 2px solid #e5e7eb;"></div>
                </div>
            </div>

            <!-- أزرار التحكم للمعلم -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                <button id="teacher-confirm-attendance" class="btn-secondary" disabled>
                    <i class="fas fa-check-circle ml-2"></i>
                    تأكيد الحضور
                </button>

                <button id="teacher-end-lesson" class="btn-danger" disabled>
                    <i class="fas fa-sign-out-alt ml-2"></i>
                    إنهاء الحصة
                </button>
            </div>

            <!-- مؤشرات جودة المعلم -->
            <div class="grid grid-cols-2 gap-3">
                <div class="quality-card">
                    <i class="fas fa-heartbeat text-red-500 text-lg mb-1"></i>
                    <p class="text-xs text-gray-600">النبضات</p>
                    <p id="teacher-heartbeat-count" class="font-bold">0</p>
                </div>
                <div class="quality-card">
                    <i class="fas fa-star text-yellow-500 text-lg mb-1"></i>
                    <p class="text-xs text-gray-600">الجودة</p>
                    <p id="teacher-quality-score" class="font-bold">100</p>
                </div>
            </div>
        </div>

        <!-- لوحة الطالب -->
        <div class="student-panel p-6">
            <h4 class="text-lg font-bold text-gray-800 mb-4">
                <i class="fas fa-user-graduate text-green-600 ml-2"></i>
                مراقبة الطالب
            </h4>
            
            {% if student_monitoring %}
            <!-- حالة الطالب -->
            <div id="student-status" class="mb-4">
                <div class="flex items-center justify-between bg-white rounded-lg p-4 shadow-sm">
                    <div class="flex items-center">
                        <div id="student-status-indicator" class="w-3 h-3 rounded-full bg-gray-400 ml-3 status-indicator"></div>
                        <span id="student-status-text" class="font-medium text-gray-700">{{ student_monitoring.get_status_display }}</span>
                    </div>
                    <div class="text-sm text-gray-600">
                        <i class="fas fa-user ml-1"></i>
                        {{ live_lesson.student.get_full_name }}
                    </div>
                </div>
            </div>

            <!-- مؤشرات جودة الطالب -->
            <div class="grid grid-cols-3 gap-3 mb-4">
                <div class="quality-card">
                    <i class="fas fa-heartbeat text-red-500 text-lg mb-1"></i>
                    <p class="text-xs text-gray-600">النبضات</p>
                    <p id="student-heartbeat-count" class="font-bold">{{ student_monitoring.total_heartbeats_received }}</p>
                </div>
                <div class="quality-card">
                    <i class="fas fa-signal text-blue-500 text-lg mb-1"></i>
                    <p class="text-xs text-gray-600">الاتصال</p>
                    <p id="student-connection-quality" class="font-bold">ممتاز</p>
                </div>
                <div class="quality-card">
                    <i class="fas fa-star text-yellow-500 text-lg mb-1"></i>
                    <p class="text-xs text-gray-600">الجودة</p>
                    <p id="student-quality-score" class="font-bold">{{ student_monitoring.quality_score|floatformat:0 }}</p>
                </div>
            </div>

            <!-- تحذيرات الطالب -->
            {% if student_monitoring.warnings_issued > 0 %}
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-3">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle text-yellow-600 ml-2"></i>
                    <span class="text-sm font-medium text-yellow-800">
                        تحذيرات: {{ student_monitoring.warnings_issued }}
                    </span>
                </div>
            </div>
            {% endif %}

            <!-- مخاطر التلاعب -->
            {% if student_monitoring.get_fraud_risk_level != 'none' %}
            <div class="bg-red-50 border border-red-200 rounded-lg p-3">
                <div class="flex items-center">
                    <i class="fas fa-shield-alt text-red-600 ml-2"></i>
                    <span class="text-sm font-medium text-red-800">
                        مستوى خطر التلاعب: {{ student_monitoring.get_fraud_risk_level }}
                    </span>
                </div>
            </div>
            {% endif %}

            {% else %}
            <!-- الطالب لم ينضم بعد -->
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
                <i class="fas fa-user-clock text-gray-400 text-3xl mb-2"></i>
                <p class="text-gray-600">الطالب لم ينضم للحصة بعد</p>
                <p class="text-sm text-gray-500">سيظهر هنا عند انضمامه</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- تعليمات للمعلم -->
    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <h5 class="font-bold text-green-800 mb-2">
            <i class="fas fa-info-circle ml-2"></i>
            تعليمات للمعلم المحدثة
        </h5>
        <ol class="text-sm text-green-700 space-y-1">
            <li>1. اضغط على "بدء" لتشغيل Jitsi في هذه الصفحة</li>
            <li>2. انضم للحصة وفعل الكاميرا والمايك</li>
            <li>3. اضغط "تأكيد الحضور" بعد الانضمام</li>
            <li>4. راقب حالة الطالب من اللوحة الجانبية</li>
            <li>5. تأكد من حضور الطالب لمدة 15 دقيقة على الأقل</li>
            <li>6. ابق هذه الصفحة مفتوحة طوال الحصة للمراقبة</li>
        </ol>
    </div>
    {% endif %}
</div>

{% csrf_token %}

<script>
// تمرير معرف الحصة للـ JavaScript
window.lessonId = {{ live_lesson.id }};
window.userRole = 'teacher';
</script>
{% endblock %}

{% block extra_js %}
<!-- Jitsi Meet API -->
<script src="https://meet.jit.si/external_api.js"></script>

<!-- ملفات JavaScript المحدثة -->
<script src="{% static 'js/jitsi-integration.js' %}"></script>
<script src="{% static 'js/teacher_attendance_monitor.js' %}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة نظام المراقبة للمعلم
    const monitor = new RealTimeAttendanceMonitor(
        {{ live_lesson.id }},
        'live',
        '{{ request.user.get_full_name }}'
    );

    // تخصيص حاوي Jitsi للمعلم
    monitor.jitsiContainerId = 'teacher-jitsi-container';
    monitor.startButtonId = 'teacher-start-jitsi';
    monitor.confirmButtonId = 'teacher-confirm-attendance';
    monitor.endButtonId = 'teacher-end-lesson';
    monitor.statusTextId = 'teacher-status-text';
    monitor.statusIndicatorId = 'teacher-status-indicator';

    // إعداد أزرار المعلم
    setupTeacherButtons(monitor);

    console.log('✅ تم تهيئة نظام المراقبة للمعلم');
});

function setupTeacherButtons(monitor) {
    // زر بدء الحصة
    const startButton = document.getElementById('teacher-start-jitsi');
    if (startButton) {
        startButton.onclick = () => {
            startButton.disabled = true;
            startButton.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري البدء...';

            monitor.startJitsiLesson().then(() => {
                // تفعيل أزرار التحكم بعد البدء
                setTimeout(() => {
                    const confirmBtn = document.getElementById('teacher-confirm-attendance');
                    const endBtn = document.getElementById('teacher-end-lesson');

                    if (confirmBtn) {
                        confirmBtn.disabled = false;
                        confirmBtn.classList.remove('opacity-50');
                    }

                    if (endBtn) {
                        endBtn.disabled = false;
                        endBtn.classList.remove('opacity-50');
                    }

                    // إخفاء زر البدء
                    startButton.style.display = 'none';
                }, 3000);
            }).catch((error) => {
                console.error('خطأ في بدء الحصة:', error);
                startButton.disabled = false;
                startButton.innerHTML = '<i class="fas fa-play ml-2"></i>بدء الحصة';
            });
        };
    }

    // زر تأكيد الحضور
    const confirmButton = document.getElementById('teacher-confirm-attendance');
    if (confirmButton) {
        confirmButton.onclick = () => monitor.confirmAttendance();
    }

    // زر إنهاء الحصة
    const endButton = document.getElementById('teacher-end-lesson');
    if (endButton) {
        endButton.onclick = () => {
            if (confirm('هل أنت متأكد من إنهاء الحصة؟')) {
                monitor.markLeave();
            }
        };
    }
}
</script>
{% endblock %}
